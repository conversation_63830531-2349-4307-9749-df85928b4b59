import socket
import sys
import requests
import time
from urllib.parse import urlparse

def check_github_connection():
    """诊断连接问题的核心函数"""
    print("🛠️ 开始诊断GitHub连接问题...")
    
    # 步骤1：DNS解析检测
    try:
        gh_ip = socket.gethostbyname('github.com')
        print(f"✅ DNS解析成功 - GitHub.com IP地址: {gh_ip}")
    except socket.gaierror:
        print("❌ DNS解析失败! 尝试以下方案：")
        print("1. 临时更换DNS：使用nslookup手动设置为*******或*********")
        print("2. 修改hosts文件（脚本将尝试自动修复）")
        return False

    # 步骤2：TCP端口连通性测试
    s = socket.socket()
    try:
        s.settimeout(3)
        s.connect((gh_ip, 443))
        print("✅ 443端口连通性正常")
    except socket.error:
        print("❌ 无法连接到443端口! 可能原因：")
        print("1. 防火墙阻挡 2. 代理配置错误 3. 网络运营商限制")
        return False
    finally:
        s.close()

    # 步骤3：HTTP请求测试
    try:
        resp = requests.get('https://github.com', timeout=5)
        if resp.status_code == 200:
            print("✅ HTTP访问正常")
            return True
        else:
            print(f"⚠️ 非预期状态码: {resp.status_code}")
    except requests.exceptions.SSLError:
        print("❌ SSL证书验证失败！")
    except requests.exceptions.Timeout:
        print("⌛ 请求超时，可能网络延迟过高")
    except requests.exceptions.ProxyError:
        print("🛑 代理服务器配置错误")
    except requests.exceptions.ConnectionError:
        print("🔌 连接意外中断")
    
    return False

def auto_fix_hosts():
    """自动更新hosts文件（需要管理员权限）"""
    print("\n🚀 尝试自动修复hosts配置...")
    new_hosts = [
        '# GitHub Start',
        '************ github.com',
        '************* gist.github.com',
        '************ api.github.com',
        '# GitHub End'
    ]
    
    try:
        with open('/etc/hosts' if sys.platform != 'win32' else r'C:\Windows\System32\drivers\etc\hosts', 'a+') as f:
            content = f.read()
            if 'github.com' not in content:
                f.write('\n' + '\n'.join(new_hosts))
                print("📝 Hosts文件更新成功，请稍后重试")
                return True
            else:
                print("⏩ Hosts中已存在GitHub配置")
                return False
    except PermissionError:
        print("🔒 需要管理员权限运行！请：")
        print("Windows：右键使用『以管理员身份运行』")
        print("Mac/Linux：在终端执行 sudo python script.py")
        return False

def smart_retry(max_attempts=3):
    """智能重试机制"""
    for i in range(max_attempts):
        print(f"\n🔁 第 {i+1} 次连接尝试...")
        if check_github_connection():
            return True
        time.sleep(2**i)  # 指数退避
    return False

if __name__ == '__main__':
    if not smart_retry():
        if auto_fix_hosts():
            print("\n🔄 修复后重新检测...")
            smart_retry()
    print("\n👉 更多网络优化技巧，请关注我们的频道获取实时更新！")