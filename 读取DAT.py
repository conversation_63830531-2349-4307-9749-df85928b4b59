import os
import datetime
import struct
import pytz

def read_latest_records(file_path, num_records=10):
    try:
        with open(file_path, 'rb') as f:
            f.seek(0, os.SEEK_END)
            file_size = f.tell()
            bytes_to_read = num_records * 8
            if file_size < bytes_to_read:
                bytes_to_read = file_size
            seek_position = file_size - bytes_to_read
            if seek_position < 0:
                seek_position = 0
            f.seek(seek_position, os.SEEK_SET)
            data = f.read()
            records = []
            for i in range(0, len(data), 8):
                if i + 8 > len(data):
                    continue
                timestamp_bytes = data[i:i+4]
                metric_bytes = data[i+4:i+8]
                timestamp = int.from_bytes(timestamp_bytes, byteorder='little')
                metric = struct.unpack('<f', metric_bytes)[0]
                utc_time = datetime.datetime.fromtimestamp(timestamp, tz=datetime.timezone.utc)
                beijing_time = utc_time.astimezone(pytz.timezone('Asia/Shanghai'))
                records.append((beijing_time, metric))
            records.reverse()
            return records
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        return []

# 使用示例
if __name__ == "__main__":
    file_path = r'E:\dzh2\USERDATA\SelfData\190人气\SH603200.dat'  # 替换为实际文件路径
    #file_path = r'E:\dzh2\USERDATA\SelfData\所属板块强度\SZ301550.dat'  # 替换为实际文件路径
    records = read_latest_records(file_path, num_records=10)
    for record in records:
        print(f"时间: {record[0].strftime('%Y-%m-%d %H:%M:%S')}, 人气值: {record[1]}")
