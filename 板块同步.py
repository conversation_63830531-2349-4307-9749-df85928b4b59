import os
import time

# 文件路径定义
file_pairs = [
    (r'E:\dzh3\USERDATA\block\自选股表删个股.BLK', r'E:\dzh4\USERDATA\block\自选股表删个股.BLK'),
    (r'E:\dzh3\USERDATA\block\自选股表删指数.BLK', r'E:\dzh4\USERDATA\block\自选股删指数.BLK')
    #(r'E:\dzh4\USERDATA\block\自选股80.BLK', r'E:\dzh2\USERDATA\block\自选股80.BLK'),
    #(r'E:\dzh4\USERDATA\block\自选股81.BLK', r'E:\dzh2\USERDATA\block\自选股81.BLK'),
    #(r'E:\dzh4\USERDATA\block\自选股82.BLK', r'E:\dzh2\USERDATA\block\自选股82.BLK'),
    #(r'E:\dzh4\USERDATA\block\自选股83.BLK', r'E:\dzh2\USERDATA\block\自选股83.BLK')
]

def sync_files():
    # 首次运行直接同步
    for source_path, target_path in file_pairs:
        try:
            with open(source_path, 'rb') as source_file:
                content = source_file.read()

            with open(target_path, 'wb') as target_file:
                target_file.write(content)
                print(f"首次同步完成：{os.path.basename(source_path)} -> {os.path.basename(target_path)}")
        except FileNotFoundError:
            print(f"源文件未找到：{source_path}")
        except IOError:
            print(f"读取或写入文件时发生错误：{source_path} -> {target_path}")
        except Exception as e:
            print(f"发生未知错误：{e}")

# 首次同步
sync_files()

# 循环检查文件是否有变动
while True:
    for source_path, target_path in file_pairs:
        try:
            with open(source_path, 'rb') as source_file:
                source_content = source_file.read()

            with open(target_path, 'rb') as target_file:
                target_content = target_file.read()

            if source_content != target_content:
                with open(target_path, 'wb') as target_file:
                    target_file.write(source_content)
                    print(f"同步完成：{os.path.basename(source_path)} -> {os.path.basename(target_path)}")
        except FileNotFoundError:
            print(f"源文件未找到：{source_path}")
        except IOError:
            print(f"读取或写入文件时发生错误：{source_path} -> {target_path}")
        except Exception as e:
            print(f"发生未知错误：{e}")

    # 每隔0.5秒检查一次
    time.sleep(0.5)