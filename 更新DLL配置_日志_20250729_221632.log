2025-07-29 22:16:32,537 - INFO - setup_logging:26 - 日志文件已创建: E:\工具\更新DLL配置_日志_20250729_221632.log
2025-07-29 22:16:32,538 - INFO - <module>:31 - ==================================================
2025-07-29 22:16:32,538 - INFO - <module>:32 - 开始执行更新DLL配置脚本
2025-07-29 22:16:32,538 - INFO - <module>:33 - ==================================================
2025-07-29 22:16:32,538 - INFO - <module>:99 - 步骤1: 读取Excel文件
2025-07-29 22:16:32,538 - INFO - read_excel_file:48 - 准备读取Excel文件: E:\助手\DDL配置.xlsx
2025-07-29 22:16:32,539 - DEBUG - check_file_exists:37 - 检查Excel文件是否存在: E:\助手\DDL配置.xlsx
2025-07-29 22:16:32,539 - INFO - check_file_exists:39 - ✓ Excel文件存在: E:\助手\DDL配置.xlsx
2025-07-29 22:16:32,540 - INFO - read_excel_file:57 - Excel文件大小: 17634 字节
2025-07-29 22:16:32,540 - DEBUG - read_excel_file:60 - 开始读取Excel文件...
2025-07-29 22:16:32,958 - INFO - read_excel_file:64 - Excel读取成功!
2025-07-29 22:16:32,959 - INFO - read_excel_file:65 - 数据形状: (204, 8) (行数: 204, 列数: 8)
2025-07-29 22:16:32,959 - DEBUG - read_excel_file:66 - 列索引: [0, 1, 2, 3, 4, 5, 6, 7]
2025-07-29 22:16:32,959 - DEBUG - read_excel_file:69 - Excel文件前5行数据:
2025-07-29 22:16:32,959 - DEBUG - read_excel_file:71 -   行0: ['[作者信息]', '[作者信息]', '[作者信息]', nan, 'E:\\dzh2', 'E:\\dzh2\\kingwa.log', nan, 'E:\\dzh2\\USERDATA\\block']
2025-07-29 22:16:32,960 - DEBUG - read_excel_file:71 -   行1: ['作者=KINGWA,风影', '作者=KINGWA,风影', '作者=KINGWA,风影', nan, 'E:\\dzh3', 'E:\\dzh3\\\\kingwa.log', nan, 'E:\\dzh3\\USERDATA\\block']
2025-07-29 22:16:32,960 - DEBUG - read_excel_file:71 -   行2: ['QQ=172062200  ', 'QQ=172062200  ', 'QQ=172062200  E盘', nan, 'E:\\dzh4', 'E:\\dzh4\\kingwa.log', nan, 'E:\\dzh4\\USERDATA\\block']
2025-07-29 22:16:32,960 - DEBUG - read_excel_file:71 -   行3: [nan, nan, nan, nan, 'D:\\wo', nan, nan, nan]
2025-07-29 22:16:32,961 - DEBUG - read_excel_file:71 -   行4: ['[板块名]', '[板块名]', '[板块名]', nan, nan, nan, nan, nan]
2025-07-29 22:16:32,961 - INFO - write_ini_files:108 - 步骤2: 开始写入INI文件
2025-07-29 22:16:32,961 - INFO - write_ini_files:117 - 需要处理的INI文件数量: 3
2025-07-29 22:16:32,961 - INFO - write_ini_files:118 - Excel数据列数: 8
2025-07-29 22:16:32,962 - WARNING - write_ini_files:125 - Excel列数(8)多于INI文件数(3)
2025-07-29 22:16:32,962 - WARNING - write_ini_files:126 - 部分Excel列数据将被忽略
2025-07-29 22:16:32,962 - INFO - write_ini_files:133 - 处理第1个INI文件: E:\dzh2\KINGWA.ini
2025-07-29 22:16:32,964 - INFO - write_ini_files:154 - 原文件已备份到: E:\dzh2\KINGWA.ini.backup_20250729_221632
2025-07-29 22:16:32,968 - INFO - write_ini_files:162 - 列1数据统计:
2025-07-29 22:16:32,968 - INFO - write_ini_files:163 -   总行数: 204
2025-07-29 22:16:32,968 - INFO - write_ini_files:164 -   有效行数: 169
2025-07-29 22:16:32,969 - INFO - write_ini_files:165 -   空值行数: 35
2025-07-29 22:16:32,969 - DEBUG - write_ini_files:171 - 开始写入文件: E:\dzh2\KINGWA.ini
2025-07-29 22:16:32,971 - DEBUG - write_ini_files:180 -   写入第1行: [作者信息]
2025-07-29 22:16:32,972 - DEBUG - write_ini_files:180 -   写入第2行: 作者=KINGWA,风影
2025-07-29 22:16:32,972 - DEBUG - write_ini_files:180 -   写入第3行: QQ=172062200  
2025-07-29 22:16:32,973 - DEBUG - write_ini_files:180 -   写入第4行: [板块名]
2025-07-29 22:16:32,973 - DEBUG - write_ini_files:180 -   写入第5行: ;板块序号=板块名称,DZH2
2025-07-29 22:16:32,973 - DEBUG - write_ini_files:180 -   写入第6行: 0=自选股
2025-07-29 22:16:32,973 - DEBUG - write_ini_files:180 -   写入第7行: 1=自选股1
2025-07-29 22:16:32,974 - DEBUG - write_ini_files:180 -   写入第8行: 2=自选股2
2025-07-29 22:16:32,974 - DEBUG - write_ini_files:180 -   写入第9行: 3=自选股3
2025-07-29 22:16:32,974 - DEBUG - write_ini_files:180 -   写入第10行: 4=自选股4
2025-07-29 22:16:32,975 - DEBUG - write_ini_files:180 -   写入第11行: 5=自选股5
2025-07-29 22:16:32,975 - DEBUG - write_ini_files:180 -   写入第12行: 6=自选股6
2025-07-29 22:16:32,975 - DEBUG - write_ini_files:180 -   写入第13行: 7=自选股7
2025-07-29 22:16:32,975 - DEBUG - write_ini_files:180 -   写入第14行: 8=自选股8
2025-07-29 22:16:32,976 - DEBUG - write_ini_files:180 -   写入第15行: 9=自选股9
2025-07-29 22:16:32,976 - DEBUG - write_ini_files:180 -   写入第16行: 100=自选股1出0
2025-07-29 22:16:32,977 - DEBUG - write_ini_files:180 -   写入第17行: 101=自选股1出1
2025-07-29 22:16:32,977 - DEBUG - write_ini_files:180 -   写入第18行: 102=自选股1出5
2025-07-29 22:16:32,977 - DEBUG - write_ini_files:180 -   写入第19行: 103=自选股1出15
2025-07-29 22:16:32,977 - DEBUG - write_ini_files:180 -   写入第20行: 104=自选股1出15QS
2025-07-29 22:16:32,978 - DEBUG - write_ini_files:180 -   写入第21行: 110=自选股人气
2025-07-29 22:16:32,978 - DEBUG - write_ini_files:180 -   写入第22行: 201=自选股人和板
2025-07-29 22:16:32,978 - DEBUG - write_ini_files:180 -   写入第23行: [自定义数据名]
2025-07-29 22:16:32,978 - DEBUG - write_ini_files:180 -   写入第24行: ;序号=自定义数据名
2025-07-29 22:16:32,978 - DEBUG - write_ini_files:180 -   写入第25行: 1=1临时板人
2025-07-29 22:16:32,979 - DEBUG - write_ini_files:180 -   写入第26行: 2=2临时板涨
2025-07-29 22:16:32,979 - DEBUG - write_ini_files:180 -   写入第27行: 3=3临时板增额
2025-07-29 22:16:32,979 - DEBUG - write_ini_files:180 -   写入第28行: 10=10连板高度
2025-07-29 22:16:32,980 - DEBUG - write_ini_files:180 -   写入第29行: 11=11涨停时间
2025-07-29 22:16:32,980 - DEBUG - write_ini_files:180 -   写入第30行: 12=12涨停评分
2025-07-29 22:16:32,980 - DEBUG - write_ini_files:180 -   写入第31行: 13=13一字板
2025-07-29 22:16:32,981 - DEBUG - write_ini_files:180 -   写入第32行: 14=14T字板
2025-07-29 22:16:32,981 - DEBUG - write_ini_files:180 -   写入第33行: 15=15黄金换手
2025-07-29 22:16:32,981 - DEBUG - write_ini_files:180 -   写入第34行: 22=22成交金额
2025-07-29 22:16:32,981 - DEBUG - write_ini_files:180 -   写入第35行: 27=27板块最高人
2025-07-29 22:16:32,982 - DEBUG - write_ini_files:180 -   写入第36行: 28=28板块增量
2025-07-29 22:16:32,982 - DEBUG - write_ini_files:180 -   写入第37行: 29=29板块最高增量
2025-07-29 22:16:32,983 - DEBUG - write_ini_files:180 -   写入第38行: ;板块相关数据
2025-07-29 22:16:32,983 - DEBUG - write_ini_files:180 -   写入第39行: 30=30板块主升
2025-07-29 22:16:32,984 - DEBUG - write_ini_files:180 -   写入第40行: 31=31板块短线
2025-07-29 22:16:32,984 - DEBUG - write_ini_files:180 -   写入第41行: 32=32板块底
2025-07-29 22:16:32,984 - DEBUG - write_ini_files:180 -   写入第42行: 33=33板块买力
2025-07-29 22:16:32,985 - DEBUG - write_ini_files:180 -   写入第43行: 34=34板块大涨统
2025-07-29 22:16:32,985 - DEBUG - write_ini_files:180 -   写入第44行: 35=35板块涨停数
2025-07-29 22:16:32,985 - DEBUG - write_ini_files:180 -   写入第45行: 36=36板块连板数
2025-07-29 22:16:32,986 - DEBUG - write_ini_files:180 -   写入第46行: 37=37板块增量
2025-07-29 22:16:32,986 - DEBUG - write_ini_files:180 -   写入第47行: 38=38板块人气和
2025-07-29 22:16:32,986 - DEBUG - write_ini_files:180 -   写入第48行: 39=39板块最高板
2025-07-29 22:16:32,987 - DEBUG - write_ini_files:180 -   写入第49行: ;VPT相关数据
2025-07-29 22:16:32,987 - DEBUG - write_ini_files:180 -   写入第50行: 40=40VPT个股
2025-07-29 22:16:32,987 - DEBUG - write_ini_files:180 -   写入第51行: 41=41VPT板块
2025-07-29 22:16:32,988 - DEBUG - write_ini_files:180 -   写入第52行: 43=43VPT结构
2025-07-29 22:16:32,988 - DEBUG - write_ini_files:180 -   写入第53行: 44=44大盘VPT
2025-07-29 22:16:32,988 - DEBUG - write_ini_files:180 -   写入第54行: 45=45VPT板块结构
2025-07-29 22:16:32,988 - DEBUG - write_ini_files:180 -   写入第55行: 46=46VPT平均
2025-07-29 22:16:32,989 - DEBUG - write_ini_files:180 -   写入第56行: 47=47VPTvpt负面积
2025-07-29 22:16:32,989 - DEBUG - write_ini_files:180 -   写入第57行: 48=48VPTBK平均
2025-07-29 22:16:32,989 - DEBUG - write_ini_files:180 -   写入第58行: 49=49VPTBK负面积
2025-07-29 22:16:32,990 - DEBUG - write_ini_files:180 -   写入第59行: 50=50VPTE个股
2025-07-29 22:16:32,990 - DEBUG - write_ini_files:180 -   写入第60行: 60=60竞价DDE
2025-07-29 22:16:32,990 - DEBUG - write_ini_files:180 -   写入第61行: 61=61竞价DDV
2025-07-29 22:16:32,990 - DEBUG - write_ini_files:180 -   写入第62行: 110=110东财人
2025-07-29 22:16:32,990 - DEBUG - write_ini_files:180 -   写入第63行: 111=111同花人
2025-07-29 22:16:32,991 - DEBUG - write_ini_files:180 -   写入第64行: 112=112涨停人气
2025-07-29 22:16:32,991 - DEBUG - write_ini_files:180 -   写入第65行: 113=113涨停人气
2025-07-29 22:16:32,991 - DEBUG - write_ini_files:180 -   写入第66行: 114=114人气评分
2025-07-29 22:16:32,991 - DEBUG - write_ini_files:180 -   写入第67行: 115=115涨停人气
2025-07-29 22:16:32,992 - DEBUG - write_ini_files:180 -   写入第68行: 116=情绪
2025-07-29 22:16:32,993 - DEBUG - write_ini_files:180 -   写入第69行: 117=情绪B
2025-07-29 22:16:32,994 - DEBUG - write_ini_files:180 -   写入第70行: 120=120资金趋势
2025-07-29 22:16:32,994 - DEBUG - write_ini_files:180 -   写入第71行: 121=121资金趋势B
2025-07-29 22:16:32,994 - DEBUG - write_ini_files:180 -   写入第72行: 121=121L2阈值
2025-07-29 22:16:32,994 - DEBUG - write_ini_files:180 -   写入第73行: 140=140TDHS
2025-07-29 22:16:32,995 - DEBUG - write_ini_files:180 -   写入第74行: 150=150周均线D
2025-07-29 22:16:32,995 - DEBUG - write_ini_files:180 -   写入第75行: 151=151月均线Y
2025-07-29 22:16:32,995 - DEBUG - write_ini_files:180 -   写入第76行: 152=152月均线J
2025-07-29 22:16:32,995 - DEBUG - write_ini_files:180 -   写入第77行: 153=153日均线D
2025-07-29 22:16:32,996 - DEBUG - write_ini_files:180 -   写入第78行: 160=160趋势
2025-07-29 22:16:32,996 - DEBUG - write_ini_files:180 -   写入第79行: 190=190人气
2025-07-29 22:16:32,996 - DEBUG - write_ini_files:180 -   写入第80行: 201=201板块人
2025-07-29 22:16:32,996 - DEBUG - write_ini_files:180 -   写入第81行: 202=202板块大涨
2025-07-29 22:16:32,997 - DEBUG - write_ini_files:180 -   写入第82行: 203=203板块VPT
2025-07-29 22:16:32,997 - DEBUG - write_ini_files:180 -   写入第83行: 204=204板块VPT结构
2025-07-29 22:16:32,997 - DEBUG - write_ini_files:180 -   写入第84行: 205=205板块VPT平均
2025-07-29 22:16:32,997 - DEBUG - write_ini_files:180 -   写入第85行: 206=206板块VPT负面积
2025-07-29 22:16:32,998 - DEBUG - write_ini_files:180 -   写入第86行: 213=213板块VPTLE
2025-07-29 22:16:32,998 - DEBUG - write_ini_files:180 -   写入第87行: 217=217板块增量
2025-07-29 22:16:32,998 - DEBUG - write_ini_files:180 -   写入第88行: 290=290板块主升
2025-07-29 22:16:32,999 - DEBUG - write_ini_files:180 -   写入第89行: 291=291板块回调
2025-07-29 22:16:32,999 - DEBUG - write_ini_files:180 -   写入第90行: 299=299板块人和
2025-07-29 22:16:32,999 - DEBUG - write_ini_files:180 -   写入第91行: 300=300SSBK
2025-07-29 22:16:32,999 - DEBUG - write_ini_files:180 -   写入第92行: 301=301SSBK人
2025-07-29 22:16:32,999 - DEBUG - write_ini_files:180 -   写入第93行: 302=302SSKBVPTE
2025-07-29 22:16:33,000 - DEBUG - write_ini_files:180 -   写入第94行: 303=303SSKBVPT
2025-07-29 22:16:33,000 - DEBUG - write_ini_files:180 -   写入第95行: 304=304SSKB
2025-07-29 22:16:33,000 - DEBUG - write_ini_files:180 -   写入第96行: 305=305SSKB
2025-07-29 22:16:33,000 - DEBUG - write_ini_files:180 -   写入第97行: 306=306SSKB
2025-07-29 22:16:33,001 - DEBUG - write_ini_files:180 -   写入第98行: 307=307SSKB
2025-07-29 22:16:33,002 - DEBUG - write_ini_files:180 -   写入第99行: 308=308SSKB
2025-07-29 22:16:33,003 - DEBUG - write_ini_files:180 -   写入第100行: 321=321SSBK人板时
2025-07-29 22:16:33,003 - DEBUG - write_ini_files:180 -   写入第101行: 322=322SSBK大涨板时
2025-07-29 22:16:33,003 - DEBUG - write_ini_files:180 -   写入第102行: 323=323SSBKVPT板时
2025-07-29 22:16:33,004 - DEBUG - write_ini_files:180 -   写入第103行: 390=390板合短
2025-07-29 22:16:33,004 - DEBUG - write_ini_files:180 -   写入第104行: 391=391板合主升
2025-07-29 22:16:33,004 - DEBUG - write_ini_files:180 -   写入第105行: 392=392板合回调
2025-07-29 22:16:33,004 - DEBUG - write_ini_files:180 -   写入第106行: 399=DATA399
2025-07-29 22:16:33,004 - DEBUG - write_ini_files:180 -   写入第107行: 511=511涨停
2025-07-29 22:16:33,005 - DEBUG - write_ini_files:180 -   写入第108行: 512=512市场高度
2025-07-29 22:16:33,005 - DEBUG - write_ini_files:180 -   写入第109行: 513=513市场高度
2025-07-29 22:16:33,005 - DEBUG - write_ini_files:180 -   写入第110行: 514=514市场高度
2025-07-29 22:16:33,005 - DEBUG - write_ini_files:180 -   写入第111行: 515=515市场高度
2025-07-29 22:16:33,006 - DEBUG - write_ini_files:180 -   写入第112行: 516=516市场高度
2025-07-29 22:16:33,006 - DEBUG - write_ini_files:180 -   写入第113行: 517=517市场高度
2025-07-29 22:16:33,007 - DEBUG - write_ini_files:180 -   写入第114行: 518=518市场高度
2025-07-29 22:16:33,007 - DEBUG - write_ini_files:180 -   写入第115行: 519=519市场高度
2025-07-29 22:16:33,007 - DEBUG - write_ini_files:180 -   写入第116行: 520=520市场高度
2025-07-29 22:16:33,008 - DEBUG - write_ini_files:180 -   写入第117行: 521=521市场高度
2025-07-29 22:16:33,008 - DEBUG - write_ini_files:180 -   写入第118行: 522=522市场高度
2025-07-29 22:16:33,008 - DEBUG - write_ini_files:180 -   写入第119行: 523=523市场高度
2025-07-29 22:16:33,009 - DEBUG - write_ini_files:180 -   写入第120行: 530=530涨幅高标
2025-07-29 22:16:33,009 - DEBUG - write_ini_files:180 -   写入第121行: 550=550A股涨停
2025-07-29 22:16:33,009 - DEBUG - write_ini_files:180 -   写入第122行: 551=551A股连板数
2025-07-29 22:16:33,009 - DEBUG - write_ini_files:180 -   写入第123行: 552=552空间板高度
2025-07-29 22:16:33,009 - DEBUG - write_ini_files:180 -   写入第124行: 553=553A股大涨
2025-07-29 22:16:33,010 - DEBUG - write_ini_files:180 -   写入第125行: 554=554炸板家数
2025-07-29 22:16:33,010 - DEBUG - write_ini_files:180 -   写入第126行: 555=555反包涨停
2025-07-29 22:16:33,010 - DEBUG - write_ini_files:180 -   写入第127行: 560=560A股跌停
2025-07-29 22:16:33,010 - DEBUG - write_ini_files:180 -   写入第128行: 561=561A股连跌
2025-07-29 22:16:33,010 - DEBUG - write_ini_files:180 -   写入第129行: 563=563A股大跌
2025-07-29 22:16:33,011 - DEBUG - write_ini_files:180 -   写入第130行: 570=570空间板高度
2025-07-29 22:16:33,011 - DEBUG - write_ini_files:180 -   写入第131行: 580=580平均股价VPT
2025-07-29 22:16:33,011 - DEBUG - write_ini_files:180 -   写入第132行: 581=581上证VPT
2025-07-29 22:16:33,011 - DEBUG - write_ini_files:180 -   写入第133行: 582=582深证VPT
2025-07-29 22:16:33,012 - DEBUG - write_ini_files:180 -   写入第134行: 585=585天时生态
2025-07-29 22:16:33,012 - DEBUG - write_ini_files:180 -   写入第135行: 586=586天时外因
2025-07-29 22:16:33,012 - DEBUG - write_ini_files:180 -   写入第136行: 587=587天时内因
2025-07-29 22:16:33,012 - DEBUG - write_ini_files:180 -   写入第137行: 588=588天时外因
2025-07-29 22:16:33,013 - DEBUG - write_ini_files:180 -   写入第138行: 590=590涨停天时
2025-07-29 22:16:33,014 - DEBUG - write_ini_files:180 -   写入第139行: 599=599天时
2025-07-29 22:16:33,014 - DEBUG - write_ini_files:180 -   写入第140行: 600=全市场板块强度
2025-07-29 22:16:33,014 - DEBUG - write_ini_files:180 -   写入第141行: 602=所属板块强度
2025-07-29 22:16:33,015 - DEBUG - write_ini_files:180 -   写入第142行: 601=SSBK板块强度
2025-07-29 22:16:33,017 - DEBUG - write_ini_files:180 -   写入第143行: 603=热点板块
2025-07-29 22:16:33,018 - DEBUG - write_ini_files:180 -   写入第144行: 604=接力
2025-07-29 22:16:33,018 - DEBUG - write_ini_files:180 -   写入第145行: 605=测试通道
2025-07-29 22:16:33,018 - DEBUG - write_ini_files:180 -   写入第146行: 998=998东财人
2025-07-29 22:16:33,019 - DEBUG - write_ini_files:180 -   写入第147行: 999=999同花人
2025-07-29 22:16:33,019 - DEBUG - write_ini_files:180 -   写入第148行: 1000=1000测试用
2025-07-29 22:16:33,019 - DEBUG - write_ini_files:180 -   写入第149行: 1001=1001测试用
2025-07-29 22:16:33,019 - DEBUG - write_ini_files:180 -   写入第150行: [字符串]
2025-07-29 22:16:33,019 - DEBUG - write_ini_files:180 -   写入第151行: 1=盘中监控
2025-07-29 22:16:33,020 - DEBUG - write_ini_files:180 -   写入第152行: 2=历史记录2
2025-07-29 22:16:33,020 - DEBUG - write_ini_files:180 -   写入第153行: [TXT文件名]
2025-07-29 22:16:33,020 - DEBUG - write_ini_files:180 -   写入第154行: 1=D:\aaa.TXT
2025-07-29 22:16:33,020 - DEBUG - write_ini_files:180 -   写入第155行: [SQLIT]
2025-07-29 22:16:33,020 - DEBUG - write_ini_files:180 -   写入第156行: #数据库名，表名，字段名，类型
2025-07-29 22:16:33,021 - DEBUG - write_ini_files:180 -   写入第157行: 1=E:\数据\东财数据.db,东财人气表,日期
2025-07-29 22:16:33,021 - DEBUG - write_ini_files:180 -   写入第158行: 2=D:\StockDB\stock.db,涨停表,股票代码
2025-07-29 22:16:33,021 - DEBUG - write_ini_files:180 -   写入第159行: 3=D:\StockDB\stock.db,涨停表,序号
2025-07-29 22:16:33,021 - DEBUG - write_ini_files:180 -   写入第160行: [路径]
2025-07-29 22:16:33,021 - DEBUG - write_ini_files:180 -   写入第161行: 通达信=D:\TDX\
2025-07-29 22:16:33,021 - DEBUG - write_ini_files:180 -   写入第162行: [TDX板块名]
2025-07-29 22:16:33,022 - DEBUG - write_ini_files:180 -   写入第163行: ;保存到通达信的板块设置,注意后面的是板块名的拼音首字母(在通达信用键盘精灵的代码)
2025-07-29 22:16:33,022 - DEBUG - write_ini_files:180 -   写入第164行: 1=RQ
2025-07-29 22:16:33,022 - DEBUG - write_ini_files:180 -   写入第165行: 2=ZXG
2025-07-29 22:16:33,023 - DEBUG - write_ini_files:180 -   写入第166行: [文件名]
2025-07-29 22:16:33,023 - DEBUG - write_ini_files:180 -   写入第167行: 1=
2025-07-29 22:16:33,023 - DEBUG - write_ini_files:180 -   写入第168行: [投资账户]
2025-07-29 22:16:33,023 - DEBUG - write_ini_files:180 -   写入第169行: 8=长江1.INV
2025-07-29 22:16:33,024 - INFO - write_ini_files:185 - ✓ 文件写入完成: E:\dzh2\KINGWA.ini
2025-07-29 22:16:33,024 - INFO - write_ini_files:186 -   实际写入行数: 169
2025-07-29 22:16:33,025 - INFO - write_ini_files:133 - 处理第2个INI文件: E:\dzh3\KINGWA.ini
2025-07-29 22:16:33,026 - INFO - write_ini_files:154 - 原文件已备份到: E:\dzh3\KINGWA.ini.backup_20250729_221633
2025-07-29 22:16:33,027 - INFO - write_ini_files:162 - 列2数据统计:
2025-07-29 22:16:33,027 - INFO - write_ini_files:163 -   总行数: 204
2025-07-29 22:16:33,028 - INFO - write_ini_files:164 -   有效行数: 89
2025-07-29 22:16:33,028 - INFO - write_ini_files:165 -   空值行数: 115
2025-07-29 22:16:33,028 - DEBUG - write_ini_files:171 - 开始写入文件: E:\dzh3\KINGWA.ini
2025-07-29 22:16:33,029 - DEBUG - write_ini_files:180 -   写入第1行: [作者信息]
2025-07-29 22:16:33,029 - DEBUG - write_ini_files:180 -   写入第2行: 作者=KINGWA,风影
2025-07-29 22:16:33,030 - DEBUG - write_ini_files:180 -   写入第3行: QQ=172062200  
2025-07-29 22:16:33,030 - DEBUG - write_ini_files:180 -   写入第4行: [板块名]
2025-07-29 22:16:33,030 - DEBUG - write_ini_files:180 -   写入第5行: ;板块序号=板块名称,DZH3
2025-07-29 22:16:33,030 - DEBUG - write_ini_files:180 -   写入第6行: 0=自选股
2025-07-29 22:16:33,030 - DEBUG - write_ini_files:180 -   写入第7行: 1=自选股1
2025-07-29 22:16:33,031 - DEBUG - write_ini_files:180 -   写入第8行: 2=自选股2
2025-07-29 22:16:33,031 - DEBUG - write_ini_files:180 -   写入第9行: 3=自选股3
2025-07-29 22:16:33,031 - DEBUG - write_ini_files:180 -   写入第10行: 4=自选股4
2025-07-29 22:16:33,031 - DEBUG - write_ini_files:180 -   写入第11行: 5=自选股5
2025-07-29 22:16:33,031 - DEBUG - write_ini_files:180 -   写入第12行: 6=自选股6
2025-07-29 22:16:33,032 - DEBUG - write_ini_files:180 -   写入第13行: 7=自选股7
2025-07-29 22:16:33,032 - DEBUG - write_ini_files:180 -   写入第14行: 8=自选股8
2025-07-29 22:16:33,032 - DEBUG - write_ini_files:180 -   写入第15行: 9=自选股9
2025-07-29 22:16:33,032 - DEBUG - write_ini_files:180 -   写入第16行: 111=自选股人气,E:\dzh4
2025-07-29 22:16:33,033 - DEBUG - write_ini_files:180 -   写入第17行: 200=自选股人气板,E:\dzh4
2025-07-29 22:16:33,033 - DEBUG - write_ini_files:180 -   写入第18行: [自定义数据名]
2025-07-29 22:16:33,034 - DEBUG - write_ini_files:180 -   写入第19行: ;序号=自定义数据名
2025-07-29 22:16:33,034 - DEBUG - write_ini_files:180 -   写入第20行: 1=1临时板人,E:\dzh2
2025-07-29 22:16:33,035 - DEBUG - write_ini_files:180 -   写入第21行: 10=10连板高度,E:\dzh2
2025-07-29 22:16:33,036 - DEBUG - write_ini_files:180 -   写入第22行: 11=11涨停时间,E:\dzh2
2025-07-29 22:16:33,037 - DEBUG - write_ini_files:180 -   写入第23行: 12=12涨停评分,E:\dzh2
2025-07-29 22:16:33,037 - DEBUG - write_ini_files:180 -   写入第24行: 13=13一字板,E:\dzh2
2025-07-29 22:16:33,037 - DEBUG - write_ini_files:180 -   写入第25行: 14=14T字板,E:\dzh2
2025-07-29 22:16:33,038 - DEBUG - write_ini_files:180 -   写入第26行: 15=15黄金换手,E:\dzh2
2025-07-29 22:16:33,038 - DEBUG - write_ini_files:180 -   写入第27行: 22=22成交金额,E:\dzh2
2025-07-29 22:16:33,038 - DEBUG - write_ini_files:180 -   写入第28行: 110=110东财人,E:\dzh2
2025-07-29 22:16:33,038 - DEBUG - write_ini_files:180 -   写入第29行: 111=111同花人,E:\dzh2
2025-07-29 22:16:33,039 - DEBUG - write_ini_files:180 -   写入第30行: 112=112涨停人气,E:\dzh2
2025-07-29 22:16:33,040 - DEBUG - write_ini_files:180 -   写入第31行: 113=113涨停人气,E:\dzh2
2025-07-29 22:16:33,042 - DEBUG - write_ini_files:180 -   写入第32行: 114=114人气评分,E:\dzh2
2025-07-29 22:16:33,042 - DEBUG - write_ini_files:180 -   写入第33行: 121=121L2阈值,E:\dzh2
2025-07-29 22:16:33,043 - DEBUG - write_ini_files:180 -   写入第34行: 160=160趋势,E:\dzh2
2025-07-29 22:16:33,043 - DEBUG - write_ini_files:180 -   写入第35行: 190=190人气,E:\dzh2
2025-07-29 22:16:33,044 - DEBUG - write_ini_files:180 -   写入第36行: 280=280,E:\dzh2
2025-07-29 22:16:33,044 - DEBUG - write_ini_files:180 -   写入第37行: 281=281,E:\dzh2
2025-07-29 22:16:33,044 - DEBUG - write_ini_files:180 -   写入第38行: 511=511涨停,E:\dzh2
2025-07-29 22:16:33,044 - DEBUG - write_ini_files:180 -   写入第39行: 512=512市场高度,E:\dzh2
2025-07-29 22:16:33,044 - DEBUG - write_ini_files:180 -   写入第40行: 513=513市场高度,E:\dzh2
2025-07-29 22:16:33,045 - DEBUG - write_ini_files:180 -   写入第41行: 514=514市场高度,E:\dzh2
2025-07-29 22:16:33,045 - DEBUG - write_ini_files:180 -   写入第42行: 515=515市场高度,E:\dzh2
2025-07-29 22:16:33,046 - DEBUG - write_ini_files:180 -   写入第43行: 516=516市场高度,E:\dzh2
2025-07-29 22:16:33,046 - DEBUG - write_ini_files:180 -   写入第44行: 517=517市场高度,E:\dzh2
2025-07-29 22:16:33,046 - DEBUG - write_ini_files:180 -   写入第45行: 518=518市场高度,E:\dzh2
2025-07-29 22:16:33,047 - DEBUG - write_ini_files:180 -   写入第46行: 519=519市场高度,E:\dzh2
2025-07-29 22:16:33,047 - DEBUG - write_ini_files:180 -   写入第47行: 520=520市场高度,E:\dzh2
2025-07-29 22:16:33,047 - DEBUG - write_ini_files:180 -   写入第48行: 521=521市场高度,E:\dzh2
2025-07-29 22:16:33,048 - DEBUG - write_ini_files:180 -   写入第49行: 522=522市场高度,E:\dzh2
2025-07-29 22:16:33,048 - DEBUG - write_ini_files:180 -   写入第50行: 523=523市场高度,E:\dzh2
2025-07-29 22:16:33,048 - DEBUG - write_ini_files:180 -   写入第51行: 530=530涨幅高标,E:\dzh2
2025-07-29 22:16:33,049 - DEBUG - write_ini_files:180 -   写入第52行: 550=550A股涨停,E:\dzh2
2025-07-29 22:16:33,049 - DEBUG - write_ini_files:180 -   写入第53行: 551=551A股连板数,E:\dzh2
2025-07-29 22:16:33,049 - DEBUG - write_ini_files:180 -   写入第54行: 552=552空间板高度,E:\dzh2
2025-07-29 22:16:33,049 - DEBUG - write_ini_files:180 -   写入第55行: 553=553A股大涨,E:\dzh2
2025-07-29 22:16:33,050 - DEBUG - write_ini_files:180 -   写入第56行: 554=554炸板家数,E:\dzh2
2025-07-29 22:16:33,050 - DEBUG - write_ini_files:180 -   写入第57行: 555=555反包涨停,E:\dzh2
2025-07-29 22:16:33,050 - DEBUG - write_ini_files:180 -   写入第58行: 560=560A股跌停,E:\dzh2
2025-07-29 22:16:33,051 - DEBUG - write_ini_files:180 -   写入第59行: 561=561A股连跌,E:\dzh2
2025-07-29 22:16:33,051 - DEBUG - write_ini_files:180 -   写入第60行: 563=563A股大跌,E:\dzh2
2025-07-29 22:16:33,051 - DEBUG - write_ini_files:180 -   写入第61行: 570=570空间板高度,E:\dzh2
2025-07-29 22:16:33,052 - DEBUG - write_ini_files:180 -   写入第62行: 585=585天时生态,E:\dzh2
2025-07-29 22:16:33,052 - DEBUG - write_ini_files:180 -   写入第63行: 586=586天时外因,E:\dzh2
2025-07-29 22:16:33,052 - DEBUG - write_ini_files:180 -   写入第64行: 599=599天时,E:\dzh2
2025-07-29 22:16:33,052 - DEBUG - write_ini_files:180 -   写入第65行: 602=所属板块强度,E:\dzh2
2025-07-29 22:16:33,053 - DEBUG - write_ini_files:180 -   写入第66行: 604=接力,E:\dzh2
2025-07-29 22:16:33,053 - DEBUG - write_ini_files:180 -   写入第67行: 998=998东财人,E:\dzh2
2025-07-29 22:16:33,053 - DEBUG - write_ini_files:180 -   写入第68行: 999=999同花人,E:\dzh2
2025-07-29 22:16:33,053 - DEBUG - write_ini_files:180 -   写入第69行: 1000=1000测试用,E:\dzh2
2025-07-29 22:16:33,053 - DEBUG - write_ini_files:180 -   写入第70行: [字符串]
2025-07-29 22:16:33,054 - DEBUG - write_ini_files:180 -   写入第71行: 1=盘中监控
2025-07-29 22:16:33,054 - DEBUG - write_ini_files:180 -   写入第72行: 2=历史记录2
2025-07-29 22:16:33,054 - DEBUG - write_ini_files:180 -   写入第73行: [TXT文件名]
2025-07-29 22:16:33,054 - DEBUG - write_ini_files:180 -   写入第74行: 1=D:\aaa.TXT
2025-07-29 22:16:33,054 - DEBUG - write_ini_files:180 -   写入第75行: [SQLIT]
2025-07-29 22:16:33,055 - DEBUG - write_ini_files:180 -   写入第76行: #数据库名，表名，字段名，类型
2025-07-29 22:16:33,055 - DEBUG - write_ini_files:180 -   写入第77行: 1=D:\StockDB\stock.db,涨停表,日期
2025-07-29 22:16:33,055 - DEBUG - write_ini_files:180 -   写入第78行: 2=D:\StockDB\stock.db,涨停表,股票代码
2025-07-29 22:16:33,055 - DEBUG - write_ini_files:180 -   写入第79行: 3=D:\StockDB\stock.db,涨停表,序号
2025-07-29 22:16:33,056 - DEBUG - write_ini_files:180 -   写入第80行: [路径]
2025-07-29 22:16:33,056 - DEBUG - write_ini_files:180 -   写入第81行: 通达信=D:\TDX\
2025-07-29 22:16:33,057 - DEBUG - write_ini_files:180 -   写入第82行: [TDX板块名]
2025-07-29 22:16:33,057 - DEBUG - write_ini_files:180 -   写入第83行: ;保存到通达信的板块设置,注意后面的是板块名的拼音首字母(在通达信用键盘精灵的代码)
2025-07-29 22:16:33,057 - DEBUG - write_ini_files:180 -   写入第84行: 1=RQ
2025-07-29 22:16:33,058 - DEBUG - write_ini_files:180 -   写入第85行: 2=ZXG
2025-07-29 22:16:33,058 - DEBUG - write_ini_files:180 -   写入第86行: [文件名]
2025-07-29 22:16:33,058 - DEBUG - write_ini_files:180 -   写入第87行: 1=
2025-07-29 22:16:33,059 - DEBUG - write_ini_files:180 -   写入第88行: [投资账户]
2025-07-29 22:16:33,059 - DEBUG - write_ini_files:180 -   写入第89行: 8=长江1.INV
2025-07-29 22:16:33,059 - INFO - write_ini_files:185 - ✓ 文件写入完成: E:\dzh3\KINGWA.ini
2025-07-29 22:16:33,059 - INFO - write_ini_files:186 -   实际写入行数: 89
2025-07-29 22:16:33,060 - INFO - write_ini_files:133 - 处理第3个INI文件: E:\dzh4\KINGWA.ini
2025-07-29 22:16:33,061 - INFO - write_ini_files:154 - 原文件已备份到: E:\dzh4\KINGWA.ini.backup_20250729_221633
2025-07-29 22:16:33,061 - INFO - write_ini_files:162 - 列3数据统计:
2025-07-29 22:16:33,062 - INFO - write_ini_files:163 -   总行数: 204
2025-07-29 22:16:33,062 - INFO - write_ini_files:164 -   有效行数: 101
2025-07-29 22:16:33,062 - INFO - write_ini_files:165 -   空值行数: 103
2025-07-29 22:16:33,063 - DEBUG - write_ini_files:171 - 开始写入文件: E:\dzh4\KINGWA.ini
2025-07-29 22:16:33,064 - DEBUG - write_ini_files:180 -   写入第1行: [作者信息]
2025-07-29 22:16:33,065 - DEBUG - write_ini_files:180 -   写入第2行: 作者=KINGWA,风影
2025-07-29 22:16:33,065 - DEBUG - write_ini_files:180 -   写入第3行: QQ=172062200  E盘
2025-07-29 22:16:33,066 - DEBUG - write_ini_files:180 -   写入第4行: [板块名]
2025-07-29 22:16:33,066 - DEBUG - write_ini_files:180 -   写入第5行: ;板块序号=板块名称,DZH4
2025-07-29 22:16:33,067 - DEBUG - write_ini_files:180 -   写入第6行: 0=自选股
2025-07-29 22:16:33,068 - DEBUG - write_ini_files:180 -   写入第7行: 1=自选股1
2025-07-29 22:16:33,069 - DEBUG - write_ini_files:180 -   写入第8行: 2=自选股2
2025-07-29 22:16:33,069 - DEBUG - write_ini_files:180 -   写入第9行: 3=自选股3
2025-07-29 22:16:33,069 - DEBUG - write_ini_files:180 -   写入第10行: 4=自选股4
2025-07-29 22:16:33,069 - DEBUG - write_ini_files:180 -   写入第11行: 5=自选股5
2025-07-29 22:16:33,069 - DEBUG - write_ini_files:180 -   写入第12行: 6=自选股6
2025-07-29 22:16:33,070 - DEBUG - write_ini_files:180 -   写入第13行: 7=自选股7
2025-07-29 22:16:33,070 - DEBUG - write_ini_files:180 -   写入第14行: 8=自选股8
2025-07-29 22:16:33,070 - DEBUG - write_ini_files:180 -   写入第15行: 9=自选股9,E:\dzh2
2025-07-29 22:16:33,071 - DEBUG - write_ini_files:180 -   写入第16行: 110=自选股人气,E:\dzh2
2025-07-29 22:16:33,071 - DEBUG - write_ini_files:180 -   写入第17行: 111=自选股人气
2025-07-29 22:16:33,071 - DEBUG - write_ini_files:180 -   写入第18行: 200=自选股人气板
2025-07-29 22:16:33,072 - DEBUG - write_ini_files:180 -   写入第19行: 201=自选股人和板,E:\dzh2
2025-07-29 22:16:33,072 - DEBUG - write_ini_files:180 -   写入第20行: 300=自选股表删个股
2025-07-29 22:16:33,072 - DEBUG - write_ini_files:180 -   写入第21行: 301=自选股表删指数
2025-07-29 22:16:33,072 - DEBUG - write_ini_files:180 -   写入第22行: [自定义数据名]
2025-07-29 22:16:33,073 - DEBUG - write_ini_files:180 -   写入第23行: ;序号=自定义数据名
2025-07-29 22:16:33,073 - DEBUG - write_ini_files:180 -   写入第24行: 1=1临时板人,E:\dzh2
2025-07-29 22:16:33,073 - DEBUG - write_ini_files:180 -   写入第25行: 10=10连板高度,E:\dzh2
2025-07-29 22:16:33,074 - DEBUG - write_ini_files:180 -   写入第26行: 11=11涨停时间,E:\dzh2
2025-07-29 22:16:33,074 - DEBUG - write_ini_files:180 -   写入第27行: 12=12涨停评分,E:\dzh2
2025-07-29 22:16:33,074 - DEBUG - write_ini_files:180 -   写入第28行: 13=13一字板,E:\dzh2
2025-07-29 22:16:33,074 - DEBUG - write_ini_files:180 -   写入第29行: 14=14T字板,E:\dzh2
2025-07-29 22:16:33,075 - DEBUG - write_ini_files:180 -   写入第30行: 15=15黄金换手,E:\dzh2
2025-07-29 22:16:33,075 - DEBUG - write_ini_files:180 -   写入第31行: 22=22成交金额,E:\dzh2
2025-07-29 22:16:33,075 - DEBUG - write_ini_files:180 -   写入第32行: 40=40VPT个股,E:\dzh2
2025-07-29 22:16:33,075 - DEBUG - write_ini_files:180 -   写入第33行: 50=50VPTE个股,E:\dzh2
2025-07-29 22:16:33,076 - DEBUG - write_ini_files:180 -   写入第34行: 60=60竞价DDE,E:\dzh2
2025-07-29 22:16:33,076 - DEBUG - write_ini_files:180 -   写入第35行: 61=61竞价DDV,E:\dzh2
2025-07-29 22:16:33,076 - DEBUG - write_ini_files:180 -   写入第36行: 110=110东财人,E:\dzh2
2025-07-29 22:16:33,076 - DEBUG - write_ini_files:180 -   写入第37行: 111=111同花人,E:\dzh2
2025-07-29 22:16:33,082 - DEBUG - write_ini_files:180 -   写入第38行: 112=112涨停人气,E:\dzh2
2025-07-29 22:16:33,083 - DEBUG - write_ini_files:180 -   写入第39行: 113=113涨停人气,E:\dzh2
2025-07-29 22:16:33,084 - DEBUG - write_ini_files:180 -   写入第40行: 115=115涨停人气,E:\dzh2
2025-07-29 22:16:33,085 - DEBUG - write_ini_files:180 -   写入第41行: 116=情绪,E:\dzh2
2025-07-29 22:16:33,085 - DEBUG - write_ini_files:180 -   写入第42行: 117=情绪B,E:\dzh2
2025-07-29 22:16:33,085 - DEBUG - write_ini_files:180 -   写入第43行: 120=120资金趋势,E:\dzh2
2025-07-29 22:16:33,086 - DEBUG - write_ini_files:180 -   写入第44行: 121=121资金趋势B,E:\dzh2
2025-07-29 22:16:33,086 - DEBUG - write_ini_files:180 -   写入第45行: 121=121L2阈值,E:\dzh2
2025-07-29 22:16:33,086 - DEBUG - write_ini_files:180 -   写入第46行: 140=140TDHS,E:\dzh2
2025-07-29 22:16:33,086 - DEBUG - write_ini_files:180 -   写入第47行: 150=150周均线D,E:\dzh2
2025-07-29 22:16:33,087 - DEBUG - write_ini_files:180 -   写入第48行: 151=151月均线Y,E:\dzh2
2025-07-29 22:16:33,087 - DEBUG - write_ini_files:180 -   写入第49行: 152=152月均线J,E:\dzh2
2025-07-29 22:16:33,087 - DEBUG - write_ini_files:180 -   写入第50行: 160=160趋势,E:\dzh2
2025-07-29 22:16:33,087 - DEBUG - write_ini_files:180 -   写入第51行: 190=190人气,E:\dzh2
2025-07-29 22:16:33,088 - DEBUG - write_ini_files:180 -   写入第52行: 201=201板块人,E:\dzh2
2025-07-29 22:16:33,088 - DEBUG - write_ini_files:180 -   写入第53行: 213=213板块VPTLE,E:\dzh2
2025-07-29 22:16:33,088 - DEBUG - write_ini_files:180 -   写入第54行: 282=282,E:\dzh2
2025-07-29 22:16:33,088 - DEBUG - write_ini_files:180 -   写入第55行: 299=299板块人和,E:\dzh2
2025-07-29 22:16:33,088 - DEBUG - write_ini_files:180 -   写入第56行: 321=321SSBK人板时,E:\dzh2
2025-07-29 22:16:33,089 - DEBUG - write_ini_files:180 -   写入第57行: 399=DATA399,E:\dzh2
2025-07-29 22:16:33,089 - DEBUG - write_ini_files:180 -   写入第58行: 511=511涨停,E:\dzh2
2025-07-29 22:16:33,089 - DEBUG - write_ini_files:180 -   写入第59行: 550=550A股涨停,E:\dzh2
2025-07-29 22:16:33,089 - DEBUG - write_ini_files:180 -   写入第60行: 551=551A股连板数,E:\dzh2
2025-07-29 22:16:33,089 - DEBUG - write_ini_files:180 -   写入第61行: 552=552空间板高度,E:\dzh2
2025-07-29 22:16:33,090 - DEBUG - write_ini_files:180 -   写入第62行: 553=553A股大涨,E:\dzh2
2025-07-29 22:16:33,090 - DEBUG - write_ini_files:180 -   写入第63行: 554=554炸板家数,E:\dzh2
2025-07-29 22:16:33,090 - DEBUG - write_ini_files:180 -   写入第64行: 555=555反包涨停,E:\dzh2
2025-07-29 22:16:33,091 - DEBUG - write_ini_files:180 -   写入第65行: 560=560A股跌停,E:\dzh2
2025-07-29 22:16:33,091 - DEBUG - write_ini_files:180 -   写入第66行: 561=561A股连跌,E:\dzh2
2025-07-29 22:16:33,091 - DEBUG - write_ini_files:180 -   写入第67行: 563=563A股大跌,E:\dzh2
2025-07-29 22:16:33,091 - DEBUG - write_ini_files:180 -   写入第68行: 570=570空间板高度,E:\dzh2
2025-07-29 22:16:33,092 - DEBUG - write_ini_files:180 -   写入第69行: 580=580平均股价VPT,E:\dzh2
2025-07-29 22:16:33,092 - DEBUG - write_ini_files:180 -   写入第70行: 581=581上证VPT,E:\dzh2
2025-07-29 22:16:33,092 - DEBUG - write_ini_files:180 -   写入第71行: 582=582深证VPT,E:\dzh2
2025-07-29 22:16:33,092 - DEBUG - write_ini_files:180 -   写入第72行: 585=585天时生态,E:\dzh2
2025-07-29 22:16:33,093 - DEBUG - write_ini_files:180 -   写入第73行: 586=586天时外因,E:\dzh2
2025-07-29 22:16:33,093 - DEBUG - write_ini_files:180 -   写入第74行: 587=587天时内因,E:\dzh2
2025-07-29 22:16:33,093 - DEBUG - write_ini_files:180 -   写入第75行: 590=590涨停天时,E:\dzh2
2025-07-29 22:16:33,093 - DEBUG - write_ini_files:180 -   写入第76行: 599=599天时,E:\dzh2
2025-07-29 22:16:33,094 - DEBUG - write_ini_files:180 -   写入第77行: 602=所属板块强度,E:\dzh2
2025-07-29 22:16:33,094 - DEBUG - write_ini_files:180 -   写入第78行: 604=接力,E:\dzh2
2025-07-29 22:16:33,094 - DEBUG - write_ini_files:180 -   写入第79行: 998=998东财人,E:\dzh2
2025-07-29 22:16:33,094 - DEBUG - write_ini_files:180 -   写入第80行: 999=999同花人,E:\dzh2
2025-07-29 22:16:33,095 - DEBUG - write_ini_files:180 -   写入第81行: 1000=1000测试用,E:\dzh2
2025-07-29 22:16:33,095 - DEBUG - write_ini_files:180 -   写入第82行: [字符串]
2025-07-29 22:16:33,095 - DEBUG - write_ini_files:180 -   写入第83行: 1=盘中监控
2025-07-29 22:16:33,095 - DEBUG - write_ini_files:180 -   写入第84行: 2=历史记录2
2025-07-29 22:16:33,096 - DEBUG - write_ini_files:180 -   写入第85行: [TXT文件名]
2025-07-29 22:16:33,096 - DEBUG - write_ini_files:180 -   写入第86行: 1=D:\aaa.TXT
2025-07-29 22:16:33,096 - DEBUG - write_ini_files:180 -   写入第87行: [SQLIT]
2025-07-29 22:16:33,096 - DEBUG - write_ini_files:180 -   写入第88行: #数据库名，表名，字段名，类型
2025-07-29 22:16:33,097 - DEBUG - write_ini_files:180 -   写入第89行: 1=D:\Program Files\DB Browser for SQLite\股票数据.db,人...
2025-07-29 22:16:33,097 - DEBUG - write_ini_files:180 -   写入第90行: 2=D:\StockDB\stock.db,涨停表,股票代码
2025-07-29 22:16:33,097 - DEBUG - write_ini_files:180 -   写入第91行: 3=D:\StockDB\stock.db,涨停表,序号
2025-07-29 22:16:33,098 - DEBUG - write_ini_files:180 -   写入第92行: [路径]
2025-07-29 22:16:33,098 - DEBUG - write_ini_files:180 -   写入第93行: 通达信=D:\TDX\
2025-07-29 22:16:33,098 - DEBUG - write_ini_files:180 -   写入第94行: [TDX板块名]
2025-07-29 22:16:33,098 - DEBUG - write_ini_files:180 -   写入第95行: ;保存到通达信的板块设置,注意后面的是板块名的拼音首字母(在通达信用键盘精灵的代码)
2025-07-29 22:16:33,099 - DEBUG - write_ini_files:180 -   写入第96行: 1=RQ
2025-07-29 22:16:33,099 - DEBUG - write_ini_files:180 -   写入第97行: 2=ZXG
2025-07-29 22:16:33,099 - DEBUG - write_ini_files:180 -   写入第98行: [文件名]
2025-07-29 22:16:33,100 - DEBUG - write_ini_files:180 -   写入第99行: 1=
2025-07-29 22:16:33,101 - DEBUG - write_ini_files:180 -   写入第100行: [投资账户]
2025-07-29 22:16:33,101 - DEBUG - write_ini_files:180 -   写入第101行: 8=长江1.INV
2025-07-29 22:16:33,103 - INFO - write_ini_files:185 - ✓ 文件写入完成: E:\dzh4\KINGWA.ini
2025-07-29 22:16:33,103 - INFO - write_ini_files:186 -   实际写入行数: 101
2025-07-29 22:16:33,104 - INFO - write_ini_files:210 - ==============================
2025-07-29 22:16:33,104 - INFO - write_ini_files:211 - INI文件写入完成总结:
2025-07-29 22:16:33,104 - INFO - write_ini_files:212 -   成功处理: 3 个文件
2025-07-29 22:16:33,104 - INFO - write_ini_files:213 -   失败处理: 0 个文件
2025-07-29 22:16:33,104 - INFO - write_ini_files:214 -   总计文件: 3 个文件
2025-07-29 22:16:33,105 - INFO - write_ini_files:215 - ==============================
2025-07-29 22:16:33,105 - INFO - <module>:227 - ✓ 所有文件处理成功！
2025-07-29 22:16:33,105 - INFO - <module>:235 - ==================================================
2025-07-29 22:16:33,105 - INFO - <module>:236 - 脚本执行结束
2025-07-29 22:16:33,106 - INFO - <module>:237 - ==================================================
