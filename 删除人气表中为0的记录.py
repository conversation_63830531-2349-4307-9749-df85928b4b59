import sqlite3
from contextlib import closing

# 数据库路径
DB_PATH = r'E:\YMJATTUU\数据库\股票数据.db'

conn = None  # 初始化连接对象
try:
    with closing(sqlite3.connect(DB_PATH)) as conn:
        with closing(conn.cursor()) as cur:
            # 验证符合条件的记录数量
            cur.execute('''
                SELECT COUNT(*) 
                FROM 个股人气表 
                WHERE 综合人气评分 = 0
            ''')
            count = cur.fetchone()[0]
            print(f'✅ 找到 {count} 条综合人气评分为0的记录')

            if count > 0:
                # 执行删除操作
                confirm = input(f'确认删除{count}条记录？(y/n): ')
                if confirm.lower() == 'y':
                    conn.execute('BEGIN TRANSACTION')
                    cur.execute('''
                        DELETE FROM 个股人气表
                        WHERE 综合人气评分 = 0
                    ''')
                    conn.commit()
                    print(f'✅ 成功删除 {cur.rowcount} 条记录')
                else:
                    print('❌ 操作已取消')
            else:
                print('⚠️ 没有需要删除的记录')

except sqlite3.Error as e:
    print(f'❌ 数据库操作出错: {e}')
    if conn is not None:
        conn.rollback()

except Exception as e:
    print(f'❌ 程序异常: {e}')