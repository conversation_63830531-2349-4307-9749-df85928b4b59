# MCP服务器配置工具

这个PowerShell脚本用于安全地向Cursor的MCP配置中添加新的MCP服务器，不会覆盖现有配置，并会自动创建备份。

## 功能特点

- 自动备份现有配置
- 检查是否存在同名服务器配置
- 安全添加/更新MCP服务器配置
- 支持完整的MCP服务器配置选项

## 使用方法

### 基本用法

```powershell
.\Add-McpServer.ps1 -Name "服务器名称" -Command "命令" -Args "参数1", "参数2"
```

### 添加带环境变量的服务器

```powershell
.\Add-McpServer.ps1 -Name "context7-mcp" -Command "npx" -Args "@upstash/context7-mcp" -Env @{SMITHERY_API_KEY="your-api-key"}
```

### 添加带超时和自动批准的服务器

```powershell
.\Add-McpServer.ps1 -Name "ghidra" -Command "python" -Args "bridge_mcp_ghidra.py" -Timeout 600 -AutoApprove "list_methods", "list_classes"
```

## 参数说明

- `-Name`: MCP服务器的名称（必需）
- `-Command`: 启动MCP服务器的命令（必需）
- `-Args`: MCP服务器的参数数组（可选）
- `-Env`: MCP服务器的环境变量哈希表（可选）
- `-Timeout`: MCP服务器的超时时间（秒）（可选）
- `-AutoApprove`: MCP服务器自动批准的工具列表（可选）

## 常见MCP服务器配置示例

### Context7 MCP

```powershell
.\Add-McpServer.ps1 -Name "context7-mcp" -Command "npx" -Args "@upstash/context7-mcp" -Env @{SMITHERY_API_KEY="your-api-key"}
```

### Playwright MCP

```powershell
.\Add-McpServer.ps1 -Name "playwright" -Command "npx" -Args "@playwright/mcp@latest"
```

### MCP Feedback Enhanced

```powershell
.\Add-McpServer.ps1 -Name "mcp-feedback-enhanced" -Command "python" -Args "-m", "mcp_feedback_enhanced" -Env @{MCP_DEBUG="false"; MCP_WEB_PORT="8765"; MCP_DESKTOP_MODE="true"} -Timeout 3600 -AutoApprove "interactive_feedback"
```

## 注意事项

- 脚本会自动创建带时间戳的备份文件
- 如果存在同名服务器配置，脚本会询问是否覆盖
- 配置文件保存在 `%USERPROFILE%\.cursor\mcp.json`
