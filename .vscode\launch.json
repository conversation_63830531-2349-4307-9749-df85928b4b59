{"version": "0.2.0", "configurations": [{"name": "Python: Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "justMyCode": true, "cwd": "${workspaceFolder}", "env": {}, "envFile": "${workspaceFolder}/.env"}, {"name": "Python: 更新DLL配置", "type": "python", "request": "launch", "program": "${workspaceFolder}/更新DLL配置.py", "console": "integratedTerminal", "justMyCode": true, "cwd": "${workspaceFolder}", "env": {}, "envFile": "${workspaceFolder}/.env"}]}