#requires -Version 5.0
<#
.SYNOPSIS
    列出Cursor的MCP配置中的所有服务器。

.DESCRIPTION
    此脚本用于列出Cursor的MCP配置中的所有服务器及其详细信息。

.PARAMETER Detailed
    是否显示详细信息。

.EXAMPLE
    .\List-McpServers.ps1
    .\List-McpServers.ps1 -Detailed

.NOTES
    作者: AI助手
    日期: 2025-07-05
#>

[CmdletBinding()]
param (
    [Parameter(Mandatory = $false)]
    [switch]$Detailed
)

# 定义Cursor MCP配置文件路径
$mcpConfigPath = "$env:USERPROFILE\.cursor\mcp.json"

# 检查配置文件是否存在
if (-not (Test-Path $mcpConfigPath)) {
    Write-Host "错误: MCP配置文件不存在: $mcpConfigPath" -ForegroundColor Red
    exit
}

# 读取现有配置
try {
    $config = Get-Content -Path $mcpConfigPath -Raw | ConvertFrom-Json -ErrorAction Stop
    Write-Host "已读取MCP配置文件: $mcpConfigPath" -ForegroundColor Green
}
catch {
    Write-Host "错误: 无法读取MCP配置文件: $_" -ForegroundColor Red
    exit
}

# 检查mcpServers属性是否存在
if (-not (Get-Member -InputObject $config -Name "mcpServers" -MemberType Properties)) {
    Write-Host "错误: MCP配置文件格式不正确，缺少mcpServers属性" -ForegroundColor Red
    exit
}

# 获取服务器列表
$servers = $config.mcpServers.PSObject.Properties

if ($servers.Count -eq 0) {
    Write-Host "未找到MCP服务器配置" -ForegroundColor Yellow
    exit
}

Write-Host "`nCursor MCP服务器列表 ($($servers.Count) 个服务器):`n" -ForegroundColor Cyan

foreach ($server in $servers) {
    $name = $server.Name
    $command = $server.Value.command
    $args = $server.Value.args -join " "
    
    Write-Host "服务器名称: $name" -ForegroundColor Green
    Write-Host "命令: $command $args"
    
    if ($Detailed) {
        if (Get-Member -InputObject $server.Value -Name "env" -MemberType Properties) {
            Write-Host "环境变量:"
            foreach ($env in $server.Value.env.PSObject.Properties) {
                $envName = $env.Name
                $envValue = $env.Value
                # 对API密钥进行部分隐藏
                if ($envName -like "*API_KEY*" -or $envName -like "*SECRET*" -or $envName -like "*TOKEN*") {
                    if ($envValue.Length -gt 8) {
                        $envValue = $envValue.Substring(0, 4) + "..." + $envValue.Substring($envValue.Length - 4)
                    } else {
                        $envValue = "********"
                    }
                }
                Write-Host "  $envName = $envValue"
            }
        }
        
        if (Get-Member -InputObject $server.Value -Name "timeout" -MemberType Properties) {
            Write-Host "超时: $($server.Value.timeout) 秒"
        }
        
        if (Get-Member -InputObject $server.Value -Name "autoApprove" -MemberType Properties) {
            Write-Host "自动批准工具:"
            foreach ($tool in $server.Value.autoApprove) {
                Write-Host "  - $tool"
            }
        }
    }
    
    Write-Host ""
}
