#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新DLL配置脚本
用于验证日志功能和错误处理
"""

import os
import sys

def test_script():
    """测试主脚本"""
    print("=" * 60)
    print("测试更新DLL配置脚本")
    print("=" * 60)
    
    script_path = "更新DLL配置.py"
    
    # 检查脚本是否存在
    if not os.path.exists(script_path):
        print(f"❌ 错误: 找不到脚本文件 {script_path}")
        return False
    
    print(f"✅ 找到脚本文件: {script_path}")
    
    # 检查必要的文件
    excel_path = r'E:\助手\DDL配置.xlsx'
    print(f"\n检查Excel文件: {excel_path}")
    if os.path.exists(excel_path):
        print(f"✅ Excel文件存在")
        file_size = os.path.getsize(excel_path)
        print(f"   文件大小: {file_size} 字节")
    else:
        print(f"❌ Excel文件不存在")
        print("   这将触发文件不存在的错误处理逻辑")
    
    # 检查目标目录
    ini_dirs = [
        r'E:\dzh2',
        r'E:\dzh3', 
        r'E:\dzh4'
    ]
    
    print(f"\n检查目标目录:")
    for i, dir_path in enumerate(ini_dirs, 1):
        if os.path.exists(dir_path):
            print(f"✅ 目录{i}存在: {dir_path}")
        else:
            print(f"❌ 目录{i}不存在: {dir_path}")
    
    print(f"\n准备运行脚本...")
    print(f"日志文件将保存在当前目录中")
    print(f"文件名格式: 更新DLL配置_日志_YYYYMMDD_HHMMSS.log")
    
    # 运行脚本
    try:
        print(f"\n" + "=" * 40)
        print("开始执行脚本...")
        print("=" * 40)
        
        # 导入并运行脚本
        exec(open(script_path, encoding='utf-8').read())
        
    except SystemExit as e:
        print(f"\n脚本正常退出，退出码: {e.code}")
        return e.code == 0
        
    except Exception as e:
        print(f"\n❌ 脚本执行出错: {str(e)}")
        import traceback
        print(f"详细错误信息:\n{traceback.format_exc()}")
        return False
    
    return True

if __name__ == "__main__":
    success = test_script()
    
    print(f"\n" + "=" * 60)
    if success:
        print("✅ 测试完成")
    else:
        print("❌ 测试失败")
    print("=" * 60)
    
    # 显示生成的日志文件
    log_files = [f for f in os.listdir('.') if f.startswith('更新DLL配置_日志_') and f.endswith('.log')]
    if log_files:
        print(f"\n📋 生成的日志文件:")
        for log_file in sorted(log_files):
            print(f"   {log_file}")
            file_size = os.path.getsize(log_file)
            print(f"   大小: {file_size} 字节")
    
    input("\n按回车键退出...")
