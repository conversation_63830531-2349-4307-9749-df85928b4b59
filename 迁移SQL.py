import sqlite3

# 数据库路径配置
old_db_path = r'E:\Pro\data\选股通.db'
new_db_path = r'E:\YMJATTUU\SQL\股票数据.db'

# 连接数据库
old_conn = sqlite3.connect(old_db_path)
new_conn = sqlite3.connect(new_db_path)
old_cur = old_conn.cursor()
new_cur = new_conn.cursor()

def migrate_data(old_table, new_table, fields_map, where_clause=""):
    """通用数据迁移函数"""
    try:
        # 构建SELECT查询
        select_fields = ', '.join([f'"{k}"' for k in fields_map.keys()])
        query = f'SELECT {select_fields} FROM "{old_table}"'
        if where_clause:
            query += f' WHERE {where_clause}'
        
        # 从旧表读取数据
        old_cur.execute(query)
        rows = old_cur.fetchall()
        
        if not rows:
            print(f"{new_table}: 无数据可迁移")
            return
        
        # 构建INSERT语句
        insert_fields = ', '.join([f'"{v}"' for v in fields_map.values()])
        placeholders = ', '.join(['?'] * len(fields_map))
        new_cur.executemany(
            f'INSERT INTO "{new_table}" ({insert_fields}) VALUES ({placeholders})',
            rows
        )
        new_conn.commit()
        print(f"{new_table} 迁移完成，共迁移 {len(rows)} 条数据")
        
    except Exception as e:
        new_conn.rollback()
        print(f"{new_table} 迁移失败，错误信息: {str(e)}")

# 定义各表映射关系
migrations = [
    {
        "old_table": "股票板块关系表",
        "new_table": "个股板块关联表",
        "fields_map": {
            "日期": "日期",
            "股票代码": "股票代码",
            "板块名称": "所属板块名称"
        }
    },
    {
        "old_table": "板块基础信息表",
        "new_table": "板块信息表",
        "fields_map": {
            "日期": "日期",
            "板块名称": "板块名称",
            "板块涨幅": "板块涨幅",
            "板块消息": "板块消息"
        }
    },
    {
        "old_table": "股票基础信息表",
        "new_table": "个股解读表",
        "fields_map": {
            "日期": "日期",
            "股票代码": "股票代码",
            "个股解读": "个股解读"
        }
    },
    {
        "old_table": "个股人气",
        "new_table": "个股人气表",
        "fields_map": {
            "日期": "日期",
            "股票代码": "股票代码",
            "东财人气": "东财人气排名",
            "同花人气": "同花人气排名",
            "人气评分": "综合人气评分"
        }
    },
    {
        "old_table": "股票基础信息表",
        "new_table": "个股连板高度表",
        "fields_map": {
            "日期": "日期",
            "股票代码": "股票代码",
            "连板高度": "连板高度"
        },
        "where_clause": "连板高度 IS NOT NULL"  # 过滤空值
    }
]

# 执行所有迁移任务
for task in migrations:
    migrate_data(
        old_table=task["old_table"],
        new_table=task["new_table"],
        fields_map=task["fields_map"],
        where_clause=task.get("where_clause", "")
    )

# 关闭连接
old_conn.close()
new_conn.close()