import shutil
import os

def copy_file(src, dst):
    """复制单个文件"""
    try:
        shutil.copy2(src, dst)
        print(f"文件 {src} 已成功复制到 {dst}")
    except Exception as e:
        print(f"复制文件 {src} 到 {dst} 出错: {e}")

def copy_directory(src, dst):
    """复制整个目录的内容"""
    try:
        if not os.path.exists(dst):
            os.makedirs(dst)
        for item in os.listdir(src):
            s = os.path.join(src, item)
            d = os.path.join(dst, item)
            if os.path.isdir(s):
                copy_directory(s, d)
            else:
                shutil.copy2(s, d)
        print(f"目录 {src} 已成功复制到 {dst}")
    except Exception as e:
        print(f"复制目录 {src} 到 {dst} 出错: {e}")

# 指定要复制的文件和文件夹
files_to_copy = [
    ('E:\\dzh22\\kingwa.dll', 'E:\\dzh2\\kingwa.dll'),
    ('E:\\dzh22\\KINGWA.ini', 'E:\\dzh2\\KINGWA.ini'),
    ('E:\\dzh22\\Order.dll', 'E:\\dzh2\\Order.dll'),
    ('E:\\dzh22\\大智慧自选股格式设置.exe', 'E:\\dzh2\\大智慧自选股格式设置.exe'),
    ('E:\\dzh33\\KINGWA.ini', 'E:\\dzh3\\KINGWA.ini'),
    ('E:\\dzh44\\KINGWA.ini', 'E:\\dzh4\\KINGWA.ini'),
    ('E:\\dzh33\\大智慧自选股格式设置.exe', 'E:\\dzh3\\大智慧自选股格式设置.exe'),
    ('E:\\dzh44\\大智慧自选股格式设置.exe', 'E:\\dzh4\\大智慧自选股格式设置.exe')
]

directories_to_copy = [
    #('E:\\dzh22\\USERDATA\\SelfData\\110东财人', 'E:\\dzh2\\USERDATA\\SelfData\\110东财人'),
    #('E:\\dzh22\\USERDATA\\SelfData\\111同花人', 'E:\\dzh2\\USERDATA\\SelfData\\111同花人'),
    ('E:\\dzh22\\Newlayout', 'E:\\dzh2\\Newlayout'),
    ('E:\\dzh22\\listplan', 'E:\\dzh2\\listplan'),
    ('E:\\dzh22\\USERDATA', 'E:\\dzh2\\USERDATA'),
    ('E:\\dzh33\\USERDATA\\NewPool', 'E:\\dzh3\\USERDATA\\NewPool'),
    ('E:\\dzh44\\USERDATA\\NewPool', 'E:\\dzh4\\USERDATA\\NewPool')
]

# 执行文件复制
for src, dst in files_to_copy:
    copy_file(src, dst)

# 执行目录复制
for src, dst in directories_to_copy:
    copy_directory(src, dst)