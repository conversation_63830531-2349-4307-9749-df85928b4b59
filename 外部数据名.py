import os
import shutil

# 指定读取的文件夹路径
src_folder = r'E:\数据'
# 指定保存的文件夹路径,有同名文件则覆盖
dst_folder = r'E:\数据'

# 遍历指定路径下的所有文件
for file_name in os.listdir(src_folder):
    # 只处理扩展名为 .TXT 的文件
    if file_name.lower().endswith('.txt'):
        # 拼接文件的完整路径
        src_file_path = os.path.join(src_folder, file_name)

        # 判断文件是否需要重命名，在这里以删除文件名称左侧的15个字节为例
        new_file_name = file_name[15:]

        # 拼接新的文件名和文件路径
        dst_file_path = os.path.join(dst_folder, new_file_name)

        # 复制文件到指定位置
        shutil.copy(src_file_path, dst_file_path)

        # 删除原始文件
        os.remove(src_file_path)