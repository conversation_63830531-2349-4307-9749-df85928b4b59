@echo off
chcp 65001 >nul
echo.
echo ================================================
echo           VS Code Cache Cleaner
echo ================================================
echo.

echo [1/5] Checking VS Code processes...
tasklist /fi "imagename eq Code.exe" 2>nul | find /i "Code.exe" >nul
if %errorlevel% equ 0 (
    echo Found VS Code running, attempting to close...
    taskkill /f /im Code.exe 2>nul
    if %errorlevel% equ 0 (
        echo VS Code processes terminated successfully.
    ) else (
        echo Warning: Could not terminate VS Code processes.
    )
    timeout /t 2 >nul
) else (
    echo VS Code is not running.
)

echo.
echo [2/5] Clearing workspace storage...
if exist "%APPDATA%\Code\User\workspaceStorage" (
    echo Removing: %APPDATA%\Code\User\workspaceStorage
    rmdir /s /q "%APPDATA%\Code\User\workspaceStorage" 2>nul
    if %errorlevel% equ 0 (
        echo Successfully cleared workspace storage.
    ) else (
        echo Warning: Could not clear workspace storage.
    )
) else (
    echo Workspace storage directory not found.
)

echo.
echo [3/5] Clearing cached extensions...
if exist "%APPDATA%\Code\CachedExtensions" (
    echo Removing: %APPDATA%\Code\CachedExtensions
    rmdir /s /q "%APPDATA%\Code\CachedExtensions" 2>nul
    if %errorlevel% equ 0 (
        echo Successfully cleared cached extensions.
    ) else (
        echo Warning: Could not clear cached extensions.
    )
) else (
    echo Cached extensions directory not found.
)

echo.
echo [4/5] Clearing log files...
if exist "%APPDATA%\Code\logs" (
    echo Removing: %APPDATA%\Code\logs
    rmdir /s /q "%APPDATA%\Code\logs" 2>nul
    if %errorlevel% equ 0 (
        echo Successfully cleared log files.
    ) else (
        echo Warning: Could not clear log files.
    )
) else (
    echo Log files directory not found.
)

echo.
echo [5/5] Clearing additional cache directories...
if exist "%APPDATA%\Code\User\History" (
    echo Removing: %APPDATA%\Code\User\History
    rmdir /s /q "%APPDATA%\Code\User\History" 2>nul
)

if exist "%APPDATA%\Code\User\globalStorage" (
    echo Removing: %APPDATA%\Code\User\globalStorage
    rmdir /s /q "%APPDATA%\Code\User\globalStorage" 2>nul
)

if exist "%TEMP%\vscode-*" (
    echo Removing temporary VS Code files...
    for /d %%i in ("%TEMP%\vscode-*") do rmdir /s /q "%%i" 2>nul
)

echo.
echo ================================================
echo           Cache Cleanup Complete!
echo ================================================
echo.
echo Summary:
echo - VS Code processes: Terminated
echo - Workspace storage: Cleared
echo - Cached extensions: Cleared
echo - Log files: Cleared
echo - Additional cache: Cleared
echo.
echo You can now restart VS Code.
echo.
pause
