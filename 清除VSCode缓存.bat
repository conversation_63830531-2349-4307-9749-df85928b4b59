@echo off
echo 正在清除VS Code缓存...

REM 关闭VS Code进程
taskkill /f /im Code.exe 2>nul

REM 清除用户缓存
if exist "%APPDATA%\Code\User\workspaceStorage" (
    echo 清除工作区缓存...
    rmdir /s /q "%APPDATA%\Code\User\workspaceStorage"
)

if exist "%APPDATA%\Code\CachedExtensions" (
    echo 清除扩展缓存...
    rmdir /s /q "%APPDATA%\Code\CachedExtensions"
)

if exist "%APPDATA%\Code\logs" (
    echo 清除日志文件...
    rmdir /s /q "%APPDATA%\Code\logs"
)

echo 缓存清除完成！
echo 请重新启动VS Code
pause
