import csv
import time
from pathlib import Path
import os
import sys
from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from datetime import datetime

# ========== 全局配置 ==========
project_root = Path(__file__).absolute().parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

current_dir = Path(__file__).parent
SAVE_PATH = os.path.join(current_dir, "bsj_xgb_ie.csv")

EDGE_DRIVER_PATH = r'C:\WebDriver\edgedriver\msedgedriver.exe'
TARGET_URL = "https://xuangutong.com.cn/top-gainer"

# ========== 核心功能类修改部分 ==========
class StockCrawler:
    _driver = None
    _instance_count = 0  # 新增实例计数器

    def __init__(self):
        if self.__class__._driver is None:
            self._init_driver()
        self.__class__._instance_count += 1

    def _init_driver(self):
        print("正在初始化Edge浏览器驱动...")
        self.service = Service(EDGE_DRIVER_PATH)
        self.options = webdriver.EdgeOptions()
        
        # 优化参数配置
        anti_crawler_params = [
            "--headless",  # 无头模式
            "--disable-gpu",
            "--no-sandbox",
            "--disable-dev-shm-usage",
            "--window-size=1920,1080",
            "--incognito",
            "--disable-blink-features=AutomationControlled",
            "--force-device-scale-factor=1",
            "--lang=zh-CN",
            "--ignore-certificate-errors",
            "--log-level=3"  # 禁用浏览器日志
        ]
        
        # 禁用图片和CSS加载
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "permissions.default.stylesheet": 2
        }
        self.options.add_experimental_option("prefs", prefs)

        for param in anti_crawler_params:
            self.options.add_argument(param)

        try:
            self.__class__._driver = webdriver.Edge(
                service=self.service, 
                options=self.options
            )
            self.wait = WebDriverWait(self.__class__._driver, 20)
            print("Edge浏览器驱动初始化完成。")
        except Exception as e:
            self._cleanup_resources()
            raise RuntimeError(f"浏览器初始化失败: {str(e)}")

    @classmethod
    def _cleanup_resources(cls):
        """资源清理方法"""
        if cls._driver:
            try:
                cls._driver.quit()
            except Exception:
                pass
            cls._driver = None
        # 清理可能的残留进程
        os.system('taskkill /f /im msedge.exe >nul 2>&1')

    def fetch_data(self, retries=3):
        print("开始抓取股票数据...")
        for attempt in range(1, retries + 1):
            try:
                # 使用新标签页访问目标页面
                self.driver.execute_script("window.open('');")
                self.driver.switch_to.window(self.driver.window_handles[-1])
                self.driver.get(TARGET_URL)
                
                # 更精准的等待条件
                element = self.wait.until(EC.presence_of_element_located(
                    (By.CSS_SELECTOR, "div.topgainer-content-left div:first-child")
                ))
                self.wait.until(lambda d: len(element.text) > 100)  # 确保数据加载完成
                
                data = element.text.strip()
                
                # 关闭当前标签页
                self.driver.close()
                self.driver.switch_to.window(self.driver.window_handles[0])
                
                return data
            except Exception as e:
                print(f"抓取失败原因: {str(e)}")
                self._cleanup_resources()  # 清理异常残留
                if attempt == retries:
                    return None
                # 指数退避重试
                sleep_time = 2 ** attempt
                print(f"将在 {sleep_time} 秒后重试...")
                time.sleep(sleep_time)
                self.__class__._driver = None  # 强制重建实例
                self._init_driver()

    def __del__(self):
        """析构时自动清理"""
        self.__class__._instance_count -= 1
        if self.__class__._instance_count <= 0:
            self._cleanup_resources()

# ========== 主程序修改部分 ==========
def main() -> Path:
    crawler = None
    try:
        crawler = StockCrawler()
        if data := crawler.fetch_data():
            save_data(data)
        return Path(SAVE_PATH)
    finally:
        if crawler:
            crawler.__class__._instance_count = 0  # 强制触发清理
            crawler._cleanup_resources()


[{
	"resource": "/e:/YMJATTUU/XGB/c_xgb_ie.py",
	"owner": "python",
	"code": {
		"value": "reportUndefinedVariable",
		"target": {
			"$mid": 1,
			"path": "/microsoft/pyright/blob/main/docs/configuration.md",
			"scheme": "https",
			"authority": "github.com",
			"fragment": "reportUndefinedVariable"
		}
	},
	"severity": 4,
	"message": "未定义“save_data”",
	"source": "Pylance",
	"startLineNumber": 133,
	"startColumn": 13,
	"endLineNumber": 133,
	"endColumn": 22
}]