import os
import logging
from collections import OrderedDict

logging.basicConfig(level=logging.INFO)

class STRReader:
    """
    读取大智慧STR文件内容的类
    STR文件格式：每个记录固定512字节，前8字节为股票代码（ASCII编码），
    中间4字节为分隔符（\x00\x00\x00\x00），
    后面为GBK编码的内容，最大500字节，不足部分用\x00填充
    """
    def __init__(self, str_path):
        self.str_path = str_path
        self.block_size = 512  # 每个记录块的大小
        self.data = OrderedDict()  # 存储解析后的数据
        
        if not os.path.exists(str_path):
            logging.warning(f"文件不存在: {str_path}")
        else:
            self._load_str_file()
    
    def _load_str_file(self):
        """
        加载并解析STR文件内容
        """
        try:
            with open(self.str_path, 'rb') as f:
                while True:
                    block = f.read(self.block_size)
                    if not block:
                        break
                    
                    # 解析股票代码（前8字节，ASCII编码）
                    code = block[:8].decode('ascii').rstrip('\x00')
                    
                    # 解析内容（跳过4字节分隔符，GBK编码）
                    content_bytes = block[12:]
                    # 去除尾部的\x00填充
                    content_bytes = content_bytes.rstrip(b'\x00')
                    
                    # 解码内容（GBK编码）
                    try:
                        content = content_bytes.decode('gbk', errors='replace')
                    except Exception as e:
                        logging.warning(f"解码失败: {e}")
                        content = ''
                    
                    # 存储解析结果
                    self.data[code] = content
            
            logging.info(f"成功加载STR文件: {self.str_path}, 共{len(self.data)}条记录")
        except Exception as e:
            logging.error(f"加载STR文件失败: {e}")
    
    def get_all_data(self):
        """
        获取所有解析后的数据
        
        返回:
            dict: 股票代码为键，内容为值的字典
        """
        return self.data
    
    def get_content(self, code):
        """
        获取指定股票代码的内容
        
        参数:
            code (str): 股票代码
            
        返回:
            str: 对应的内容，如果不存在则返回None
        """
        return self.data.get(code)
    
    def get_codes(self):
        """
        获取所有股票代码列表
        
        返回:
            list: 股票代码列表
        """
        return list(self.data.keys())

# 使用示例
def read_str_file(str_path):
    """
    读取STR文件并返回解析后的数据
    
    参数:
        str_path (str): STR文件路径
        
    返回:
        dict: 股票代码为键，内容为值的字典
    """
    reader = STRReader(str_path)
    return reader.get_all_data()

# 测试代码
if __name__ == "__main__":
    # 读取全市场板块强度STR文件
    str_path = r"E:\dzh2\USERDATA\SelfData\全市场板块强度.STR"
    reader = STRReader(str_path)
    
    # 获取所有数据
    data = reader.get_all_data()
    print(f"共读取到{len(data)}条记录")
    
    # 打印前5条记录
    for i, (code, content) in enumerate(data.items()):
        if i >= 5:
            break
        print(f"股票代码: {code}, 内容: {content}")
    
    # 获取特定股票的内容
    test_code = list(data.keys())[0] if data else None
    if test_code:
        print(f"\n获取股票 {test_code} 的内容: {reader.get_content(test_code)}")