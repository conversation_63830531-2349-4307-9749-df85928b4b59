file_path = r"E:\dzh2\USERDATA\SelfData\涨停_xgb板块消息 - 副本.str"

try:
    with open(file_path, 'r', encoding='cp936') as file:
        # 逐行读取并分析
        for line_num, line in enumerate(file, 1):
            stripped_line = line.strip()
            if not stripped_line:
                continue  # 跳过空行
            
            print(f"第 {line_num} 行:")
            print(f"原始内容: {repr(line)}")

except FileNotFoundError:
    print(f"文件 {file_path} 未找到，请检查路径。")
except IOError:
    print(f"无法读取文件 {file_path}，请检查权限或文件是否损坏。")