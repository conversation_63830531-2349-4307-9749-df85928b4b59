import sqlite3

def delete_zero_height_records():
    # 数据库路径（注意使用原始字符串避免转义问题）
    db_path = r'E:\YMJATTUU\SQL\股票数据.db'
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 执行删除操作
        delete_sql = "DELETE FROM 个股连板高度表 WHERE 连板高度 = 0.0;"
        cursor.execute(delete_sql)
        
        # 提交事务并获取影响行数
        conn.commit()
        deleted_rows = cursor.rowcount
        
        print(f"成功删除 {deleted_rows} 条连板高度为0的记录")

    except sqlite3.Error as e:
        print(f"数据库操作出错: {str(e)}")
        conn.rollback()
    finally:
        # 关闭连接
        if conn:
            conn.close()

if __name__ == "__main__":
    delete_zero_height_records()