@echo off
echo ??MCP????...

set SCRIPT_DIR=%~dp0
set TARGET_DIR=%USERPROFILE%\Documents\WindowsPowerShell\Scripts

if not exist "%TARGET_DIR%" (
    mkdir "%TARGET_DIR%"
    echo ?????: %TARGET_DIR%
)

copy "%SCRIPT_DIR%\Add-McpServer.ps1" "%TARGET_DIR%"
copy "%SCRIPT_DIR%\Remove-McpServer.ps1" "%TARGET_DIR%"
copy "%SCRIPT_DIR%\List-McpServers.ps1" "%TARGET_DIR%"
copy "%SCRIPT_DIR%\README.md" "%TARGET_DIR%"

echo.
echo ???????????: %TARGET_DIR%
echo.
echo ????:
echo 1. ??PowerShell
echo 2. ????: cd %TARGET_DIR%
echo 3. ??MCP???: .\Add-McpServer.ps1 -Name "?????" -Command "??" -Args "??"
echo 4. ??MCP???: .\List-McpServers.ps1 -Detailed
echo 5. ??MCP???: .\Remove-McpServer.ps1 -Name "?????"
echo.
echo ???????: %TARGET_DIR%\README.md
echo.
pause
