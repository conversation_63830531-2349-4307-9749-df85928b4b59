#requires -Version 5.0
<#
.SYNOPSIS
    安全地向Cursor的MCP配置中添加新的MCP服务器。

.DESCRIPTION
    此脚本用于安全地向Cursor的MCP配置中添加新的MCP服务器，不会覆盖现有配置，
    并会自动创建备份。

.PARAMETER Name
    MCP服务器的名称。

.PARAMETER Command
    启动MCP服务器的命令。

.PARAMETER Args
    MCP服务器的参数数组。

.PARAMETER Env
    MCP服务器的环境变量哈希表。

.PARAMETER Timeout
    MCP服务器的超时时间（秒）。

.PARAMETER AutoApprove
    MCP服务器自动批准的工具列表。

.EXAMPLE
    .\Add-McpServer.ps1 -Name "context7-mcp" -Command "npx" -Args "@upstash/context7-mcp" -Env @{SMITHERY_API_KEY="your-api-key"}

.NOTES
    作者: AI助手
    日期: 2025-07-05
#>

[CmdletBinding()]
param (
    [Parameter(Mandatory = $true)]
    [string]$Name,
    
    [Parameter(Mandatory = $true)]
    [string]$Command,
    
    [Parameter(Mandatory = $false)]
    [string[]]$Args = @(),
    
    [Parameter(Mandatory = $false)]
    [hashtable]$Env = @{},
    
    [Parameter(Mandatory = $false)]
    [int]$Timeout = 0,
    
    [Parameter(Mandatory = $false)]
    [string[]]$AutoApprove = @()
)

# 定义Cursor MCP配置文件路径
$mcpConfigPath = "$env:USERPROFILE\.cursor\mcp.json"

# 创建备份函数
function Backup-McpConfig {
    param (
        [string]$ConfigPath
    )
    
    if (Test-Path $ConfigPath) {
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $backupPath = "$ConfigPath.backup_$timestamp"
        Copy-Item -Path $ConfigPath -Destination $backupPath -Force
        Write-Host "已创建备份: $backupPath" -ForegroundColor Green
        return $true
    }
    return $false
}

# 确保.cursor目录存在
if (-not (Test-Path "$env:USERPROFILE\.cursor")) {
    New-Item -Path "$env:USERPROFILE\.cursor" -ItemType Directory -Force | Out-Null
    Write-Host "已创建.cursor目录" -ForegroundColor Yellow
}

# 创建备份
$backupCreated = Backup-McpConfig -ConfigPath $mcpConfigPath

# 读取现有配置或创建新配置
if (Test-Path $mcpConfigPath) {
    try {
        $config = Get-Content -Path $mcpConfigPath -Raw | ConvertFrom-Json -ErrorAction Stop
        Write-Host "已读取现有MCP配置" -ForegroundColor Green
    }
    catch {
        Write-Host "现有配置文件格式错误，将创建新配置" -ForegroundColor Yellow
        $config = [PSCustomObject]@{
            mcpServers = [PSCustomObject]@{}
        }
    }
}
else {
    Write-Host "未找到现有配置，将创建新配置" -ForegroundColor Yellow
    $config = [PSCustomObject]@{
        mcpServers = [PSCustomObject]@{}
    }
}

# 确保mcpServers属性存在
if (-not (Get-Member -InputObject $config -Name "mcpServers" -MemberType Properties)) {
    Add-Member -InputObject $config -MemberType NoteProperty -Name "mcpServers" -Value ([PSCustomObject]@{})
}

# 检查是否已存在相同名称的服务器
$serverExists = $false
if ($config.mcpServers.PSObject.Properties.Name -contains $Name) {
    $serverExists = $true
    Write-Host "警告: 已存在名为'$Name'的MCP服务器配置" -ForegroundColor Yellow
    $confirmation = Read-Host "是否要覆盖? (Y/N)"
    if ($confirmation -ne 'Y') {
        Write-Host "操作已取消" -ForegroundColor Red
        exit
    }
}

# 创建新的服务器配置
$serverConfig = [PSCustomObject]@{
    command = $Command
    args = $Args
}

# 添加可选属性
if ($Env.Count -gt 0) {
    Add-Member -InputObject $serverConfig -MemberType NoteProperty -Name "env" -Value $Env
}

if ($Timeout -gt 0) {
    Add-Member -InputObject $serverConfig -MemberType NoteProperty -Name "timeout" -Value $Timeout
}

if ($AutoApprove.Count -gt 0) {
    Add-Member -InputObject $serverConfig -MemberType NoteProperty -Name "autoApprove" -Value $AutoApprove
}

# 添加或更新服务器配置
if ($serverExists) {
    # 更新现有配置
    $config.mcpServers.PSObject.Properties.Remove($Name)
}

# 添加新配置
$config.mcpServers | Add-Member -MemberType NoteProperty -Name $Name -Value $serverConfig

try {
    # 保存配置
    $config | ConvertTo-Json -Depth 10 | Set-Content -Path $mcpConfigPath -Encoding UTF8
    Write-Host "成功添加/更新MCP服务器: $Name" -ForegroundColor Green
    Write-Host "配置已保存到: $mcpConfigPath" -ForegroundColor Green
}
catch {
    Write-Host "保存配置时出错: $_" -ForegroundColor Red
    if ($backupCreated) {
        Write-Host "您可以从最近的备份恢复配置" -ForegroundColor Yellow
    }
}
