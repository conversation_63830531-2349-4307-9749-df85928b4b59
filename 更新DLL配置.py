import pandas as pd
import codecs
import sys

try:
    # 读取Excel文件
    excel_path = r'E:\助手\DDL配置.xlsx'
    df = pd.read_excel(excel_path, header=None)
except ImportError:
    print("Error: Missing optional dependency 'openpyxl'. Use pip or conda to install openpyxl.")
    sys.exit(1)

# 定义INI文件路径
ini_paths = [
    r'E:\dzh2\KINGWA.ini',
    r'E:\dzh3\KINGWA.ini',
    r'E:\dzh4\KINGWA.ini'
]

# 遍历每一列并写入对应的INI文件
for col_index, ini_path in enumerate(ini_paths):
    with codecs.open(ini_path, 'w', encoding='mbcs') as ini_file:  # 'mbcs'用于ANSI编码
        for value in df[col_index]:
            if pd.notna(value):  # 跳过空白行
                ini_file.write(f"{value}\n")