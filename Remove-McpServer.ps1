#requires -Version 5.0
<#
.SYNOPSIS
    从Cursor的MCP配置中删除指定的MCP服务器。

.DESCRIPTION
    此脚本用于从Cursor的MCP配置中安全地删除指定的MCP服务器，
    并会自动创建备份。

.PARAMETER Name
    要删除的MCP服务器的名称。

.EXAMPLE
    .\Remove-McpServer.ps1 -Name "context7-mcp-test"

.NOTES
    作者: AI助手
    日期: 2025-07-05
#>

[CmdletBinding()]
param (
    [Parameter(Mandatory = $true)]
    [string]$Name
)

# 定义Cursor MCP配置文件路径
$mcpConfigPath = "$env:USERPROFILE\.cursor\mcp.json"

# 创建备份函数
function Backup-McpConfig {
    param (
        [string]$ConfigPath
    )
    
    if (Test-Path $ConfigPath) {
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $backupPath = "$ConfigPath.backup_$timestamp"
        Copy-Item -Path $ConfigPath -Destination $backupPath -Force
        Write-Host "已创建备份: $backupPath" -ForegroundColor Green
        return $true
    }
    return $false
}

# 检查配置文件是否存在
if (-not (Test-Path $mcpConfigPath)) {
    Write-Host "错误: MCP配置文件不存在: $mcpConfigPath" -ForegroundColor Red
    exit
}

# 创建备份
$backupCreated = Backup-McpConfig -ConfigPath $mcpConfigPath

# 读取现有配置
try {
    $config = Get-Content -Path $mcpConfigPath -Raw | ConvertFrom-Json -ErrorAction Stop
    Write-Host "已读取现有MCP配置" -ForegroundColor Green
}
catch {
    Write-Host "错误: 无法读取MCP配置文件: $_" -ForegroundColor Red
    exit
}

# 检查mcpServers属性是否存在
if (-not (Get-Member -InputObject $config -Name "mcpServers" -MemberType Properties)) {
    Write-Host "错误: MCP配置文件格式不正确，缺少mcpServers属性" -ForegroundColor Red
    exit
}

# 检查服务器是否存在
if (-not ($config.mcpServers.PSObject.Properties.Name -contains $Name)) {
    Write-Host "错误: 未找到名为'$Name'的MCP服务器配置" -ForegroundColor Red
    exit
}

# 确认删除
$confirmation = Read-Host "确定要删除MCP服务器'$Name'吗? (Y/N)"
if ($confirmation -ne 'Y') {
    Write-Host "操作已取消" -ForegroundColor Yellow
    exit
}

# 删除服务器配置
$config.mcpServers.PSObject.Properties.Remove($Name)

try {
    # 保存配置
    $config | ConvertTo-Json -Depth 10 | Set-Content -Path $mcpConfigPath -Encoding UTF8
    Write-Host "成功删除MCP服务器: $Name" -ForegroundColor Green
    Write-Host "配置已保存到: $mcpConfigPath" -ForegroundColor Green
}
catch {
    Write-Host "保存配置时出错: $_" -ForegroundColor Red
    if ($backupCreated) {
        Write-Host "您可以从最近的备份恢复配置" -ForegroundColor Yellow
    }
}
