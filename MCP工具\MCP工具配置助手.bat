@echo off
echo MCP??????
echo ==================
echo.
echo 1. ??MCP?????
echo 2. ??MCP?????
echo 3. ??MCP?????
echo 4. ??????
echo 5. ??
echo.

:menu
set /p choice=????? (1-5): 

if "%choice%"=="1" goto add
if "%choice%"=="2" goto list
if "%choice%"=="3" goto remove
if "%choice%"=="4" goto help
if "%choice%"=="5" goto end

echo ??????????
goto menu

:add
echo.
echo ??PowerShell??MCP?????...
powershell -ExecutionPolicy Bypass -File "%~dp0Add-McpServer.ps1"
echo.
pause
goto menu

:list
echo.
echo ????MCP?????...
powershell -ExecutionPolicy Bypass -File "%~dp0List-McpServers.ps1" -Detailed
echo.
pause
goto menu

:remove
echo.
echo ??PowerShell??MCP?????...
powershell -ExecutionPolicy Bypass -File "%~dp0Remove-McpServer.ps1"
echo.
pause
goto menu

:help
echo.
echo ??????...
type "%~dp0README.md" | more
echo.
pause
goto menu

:end
echo ????MCP???????
exit
